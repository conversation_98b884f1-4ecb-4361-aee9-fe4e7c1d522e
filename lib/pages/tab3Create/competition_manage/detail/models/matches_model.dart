import 'package:json_annotation/json_annotation.dart';

part 'matches_model.g.dart';

@JsonSerializable()
class MatchesDetailsModel extends Object {
  @Json<PERSON><PERSON>(name: 'matchWeek')
  String matchWeek;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'matchTime')
  String matchTime;

  @J<PERSON><PERSON><PERSON>(name: 'matchDate')
  String matchDate;

  @Json<PERSON><PERSON>(name: 'matchId')
  String matchId;

  @Json<PERSON>ey(name: 'arenaId')
  String arenaId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'arenaName')
  String arenaName;

  @Json<PERSON>ey(name: 'arenaAliasName')
  String arenaAliasName;

  @Json<PERSON>ey(name: 'ArenaLogo')
  String arenaLogo;

  @Json<PERSON>ey(name: 'arenaContact')
  String arenaContact;

  @J<PERSON><PERSON><PERSON>(name: 'matchVideoPath')
  String matchVideoPath;

  @JsonKey(name: 'status')
  int status;

  @Json<PERSON>ey(name: 'locked')
  int locked;

  @<PERSON>son<PERSON><PERSON>(name: 'matchStartTime')
  String matchStartTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'matchEndTime')
  String matchEndTime;

  @J<PERSON><PERSON><PERSON>(name: 'courts')
  List<Courts>? courts;

  @Json<PERSON>ey(name: 'teams')
  List<Teams> teams;

  @JsonKey(name: 'history')
  List<GameHistory>? history;

  @JsonKey(name: 'lockStatus')
  int lockStatus;

  @JsonKey(name: 'expired')
  bool expired;

  @JsonKey(name: 'expiredTime')
  String expiredTime;

  @JsonKey(name: 'joined')
  bool joined;

  @JsonKey(name: 'matchSubscribe')
  bool matchSubscribe;

  @JsonKey(name: 'halfMatchPrice')
  String halfMatchPrice;

  @JsonKey(name: 'fullMatchPrice')
  String fullMatchPrice;

  @JsonKey(name: 'summaryStatistics')
  bool summaryStatistics;

  MatchesDetailsModel(
      this.matchTime,
      this.matchDate,
      this.matchWeek,
      this.matchId,
      this.arenaId,
      this.arenaName,
      this.arenaAliasName,
      this.arenaLogo,
      this.arenaContact,
      this.matchVideoPath,
      this.status,
      this.matchStartTime,
      this.matchEndTime,
      this.courts,
      this.teams,
      this.history,
      this.lockStatus,
      this.expired,
      this.expiredTime,
      this.joined,
      this.matchSubscribe,
      this.halfMatchPrice,
      this.fullMatchPrice,
      this.summaryStatistics,
      this.locked);

  factory MatchesDetailsModel.fromJson(Map<String, dynamic> srcJson) =>
      _$MatchesDetailsModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MatchesDetailsModelToJson(this);
}

@JsonSerializable()
class Courts extends Object {
  @JsonKey(name: 'courtId')
  String courtId;

  @JsonKey(name: 'courtName')
  String courtName;

  Courts(
    this.courtId,
    this.courtName,
  );

  factory Courts.fromJson(Map<String, dynamic> srcJson) =>
      _$CourtsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$CourtsToJson(this);
}

@JsonSerializable()
class Teams extends Object {
  @JsonKey(name: 'teamId')
  String teamId;

  @JsonKey(name: 'teamName')
  String teamName;

  @JsonKey(name: 'logo')
  String logo;

  @JsonKey(name: 'score')
  int score;

  @JsonKey(name: 'status')
  int status;

  @JsonKey(name: 'locked')
  int locked;

  @JsonKey(name: 'fragmentCount')
  int fragmentCount;

  @JsonKey(name: 'teamBest')
  TeamBest? teamBest;

  @JsonKey(name: 'teamScoreDetail')
  TeamScoreDetail teamScoreDetail;

  @JsonKey(name: 'scoreTrend')
  List<int> scoreTrend;

  @JsonKey(name: 'maxOffsetScore')
  int maxOffsetScore;

  @JsonKey(name: 'CurrentScore')
  int currentScore;

  Teams(
    this.teamId,
    this.teamName,
    this.logo,
    this.score,
    this.status,
    this.locked,
    this.fragmentCount,
    this.teamBest,
    this.teamScoreDetail,
    this.scoreTrend,
    this.maxOffsetScore,
    this.currentScore,
  );

  factory Teams.fromJson(Map<String, dynamic> srcJson) =>
      _$TeamsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamsToJson(this);
}

@JsonSerializable()
class TeamBest extends Object {
  @JsonKey(name: 'scoreKing')
  ScoreKing scoreKing;

  @JsonKey(name: 'threePointKing')
  ScoreKing threePointKing;

  @JsonKey(name: 'freeThrowKing')
  ScoreKing freeThrowKing;

  @JsonKey(name: 'assistKing')
  ScoreKing assistKing;

  @JsonKey(name: 'reboundKing')
  ScoreKing reboundKing;

  @JsonKey(name: 'mvp')
  ScoreKing mvp;

  TeamBest(
    this.scoreKing,
    this.threePointKing,
    this.freeThrowKing,
    this.assistKing,
    this.reboundKing,
    this.mvp,
  );

  factory TeamBest.fromJson(Map<String, dynamic> srcJson) =>
      _$TeamBestFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamBestToJson(this);
}

@JsonSerializable()
class ScoreKing extends Object {
  @JsonKey(name: 'playerId')
  String playerId;

  @JsonKey(name: 'photo')
  String photo;

  @JsonKey(name: 'number')
  String number;

  @JsonKey(name: 'score')
  String score;

  @JsonKey(name: 'rate')
  String rate;

  @JsonKey(name: 'hit')
  int hit;

  @JsonKey(name: 'rebound')
  int rebound;

  @JsonKey(name: 'assist')
  int assist;

  @JsonKey(name: 'locked')
  int locked;

  bool get locking => locked == 1;

  ScoreKing(
    this.playerId,
    this.photo,
    this.number,
    this.score,
    this.rate,
    this.hit,
    this.rebound,
    this.assist,
    this.locked,
  );

  factory ScoreKing.fromJson(Map<String, dynamic> srcJson) =>
      _$ScoreKingFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ScoreKingToJson(this);
}

@JsonSerializable()
class TeamScoreDetail extends Object {
  @JsonKey(name: 'totalScore')
  int totalScore;

  @JsonKey(name: 'markedScore')
  int markedScore;

  @JsonKey(name: 'reboundCount')
  int reboundCount;

  @JsonKey(name: 'offensiveReboundCount')
  int offensiveReboundCount;

  @JsonKey(name: 'defensiveReboundCount')
  int defensiveReboundCount;

  @JsonKey(name: 'assistCount')
  int assistCount;

  @JsonKey(name: 'shootCount')
  int shootCount;

  @JsonKey(name: 'shootHit')
  int shootHit;

  @JsonKey(name: 'shootRate')
  String shootRate;

  @JsonKey(name: 'threePointShootCount')
  int threePointShootCount;

  @JsonKey(name: 'threePointShootHit')
  int threePointShootHit;

  @JsonKey(name: 'threePointShootRate')
  String threePointShootRate;

  @JsonKey(name: 'twoPointShootCount')
  int twoPointShootCount;

  @JsonKey(name: 'twoPointShootHit')
  int twoPointShootHit;

  @JsonKey(name: 'twoPointShootRate')
  String twoPointShootRate;

  @JsonKey(name: 'freeThrowShootCount')
  int freeThrowShootCount;

  @JsonKey(name: 'freeThrowShootHit')
  int freeThrowShootHit;

  @JsonKey(name: 'freeThrowShootRate')
  String freeThrowShootRate;

  TeamScoreDetail(
    this.totalScore,
    this.markedScore,
    this.reboundCount,
    this.offensiveReboundCount,
    this.defensiveReboundCount,
    this.assistCount,
    this.shootCount,
    this.shootHit,
    this.shootRate,
    this.threePointShootCount,
    this.threePointShootHit,
    this.threePointShootRate,
    this.twoPointShootCount,
    this.twoPointShootHit,
    this.twoPointShootRate,
    this.freeThrowShootCount,
    this.freeThrowShootHit,
    this.freeThrowShootRate,
  );

  factory TeamScoreDetail.fromJson(Map<String, dynamic> srcJson) =>
      _$TeamScoreDetailFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TeamScoreDetailToJson(this);
}

@JsonSerializable()
class GameHistory extends Object {
  @JsonKey(name: 'leftScore')
  int leftScore;

  @JsonKey(name: 'rightScore')
  int rightScore;

  @JsonKey(name: 'winner')
  int winner;

  @JsonKey(name: 'matchTime')
  String matchTime;

  GameHistory(
    this.leftScore,
    this.rightScore,
    this.winner,
    this.matchTime,
  );

  factory GameHistory.fromJson(Map<String, dynamic> srcJson) =>
      _$GameHistoryFromJson(srcJson);

  Map<String, dynamic> toJson() => _$GameHistoryToJson(this);
}
