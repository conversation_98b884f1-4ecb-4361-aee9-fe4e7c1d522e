///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamPlayersModelPlayers {
/*
{
  "assistCount": 0,
  "assistKing": true,
  "assistRate": "string",
  "contributionValue": 0,
  "fragmentCount": 0,
  "freeThrowKing": true,
  "locked": 0,
  "mvp": true,
  "number": "string",
  "photo": "string",
  "playerId": "0",
  "playerName": "string",
  "rate": "string",
  "reboundCount": 0,
  "reboundKing": true,
  "reboundRate": "string",
  "score": 0,
  "scoreKing": true,
  "scoreRate": "string",
  "shootCount": 0,
  "shootHit": 0,
  "threePointKing": true,
  "trashTalk": "string",
  "unlockBy": "0"
} 
*/

  int? assistCount;
  bool? assistKing;
  String? assistRate;
  int? contributionValue;
  int? fragmentCount;
  bool? freeThrowKing;
  int? locked;
  bool? mvp;
  String? number;
  String? photo;
  String? playerId;
  String? playerName;
  String? rate;
  int? reboundCount;
  bool? reboundKing;
  String? reboundRate;
  int? score;
  bool? scoreKing;
  String? scoreRate;
  int? shootCount;
  int? shootHit;
  bool? threePointKing;
  String? trashTalk;
  String? unlockBy;

  TeamPlayersModelPlayers({
    this.assistCount,
    this.assistKing,
    this.assistRate,
    this.contributionValue,
    this.fragmentCount,
    this.freeThrowKing,
    this.locked,
    this.mvp,
    this.number,
    this.photo,
    this.playerId,
    this.playerName,
    this.rate,
    this.reboundCount,
    this.reboundKing,
    this.reboundRate,
    this.score,
    this.scoreKing,
    this.scoreRate,
    this.shootCount,
    this.shootHit,
    this.threePointKing,
    this.trashTalk,
    this.unlockBy,
  });
  TeamPlayersModelPlayers.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    assistKing = json['assistKing'];
    assistRate = json['assistRate']?.toString();
    contributionValue = json['contributionValue']?.toInt();
    fragmentCount = json['fragmentCount']?.toInt();
    freeThrowKing = json['freeThrowKing'];
    locked = json['locked']?.toInt();
    mvp = json['mvp'];
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    playerName = json['playerName']?.toString();
    rate = json['rate']?.toString();
    reboundCount = json['reboundCount']?.toInt();
    reboundKing = json['reboundKing'];
    reboundRate = json['reboundRate']?.toString();
    score = json['score']?.toInt();
    scoreKing = json['scoreKing'];
    scoreRate = json['scoreRate']?.toString();
    shootCount = json['shootCount']?.toInt();
    shootHit = json['shootHit']?.toInt();
    threePointKing = json['threePointKing'];
    trashTalk = json['trashTalk']?.toString();
    unlockBy = json['unlockBy']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['assistKing'] = assistKing;
    data['assistRate'] = assistRate;
    data['contributionValue'] = contributionValue;
    data['fragmentCount'] = fragmentCount;
    data['freeThrowKing'] = freeThrowKing;
    data['locked'] = locked;
    data['mvp'] = mvp;
    data['number'] = number;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['playerName'] = playerName;
    data['rate'] = rate;
    data['reboundCount'] = reboundCount;
    data['reboundKing'] = reboundKing;
    data['reboundRate'] = reboundRate;
    data['score'] = score;
    data['scoreKing'] = scoreKing;
    data['scoreRate'] = scoreRate;
    data['shootCount'] = shootCount;
    data['shootHit'] = shootHit;
    data['threePointKing'] = threePointKing;
    data['trashTalk'] = trashTalk;
    data['unlockBy'] = unlockBy;
    return data;
  }
}

class TeamPlayersModel {
/*
{
  "players": [
    {
      "assistCount": 0,
      "assistKing": true,
      "assistRate": "string",
      "contributionValue": 0,
      "fragmentCount": 0,
      "freeThrowKing": true,
      "locked": 0,
      "mvp": true,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "playerName": "string",
      "rate": "string",
      "reboundCount": 0,
      "reboundKing": true,
      "reboundRate": "string",
      "score": 0,
      "scoreKing": true,
      "scoreRate": "string",
      "shootCount": 0,
      "shootHit": 0,
      "threePointKing": true,
      "trashTalk": "string",
      "unlockBy": "0"
    }
  ],
  "teamId": "0",
  "teamName": "string"
} 
*/

  List<TeamPlayersModelPlayers?>? players;
  String? teamId;
  String? teamName;

  TeamPlayersModel({
    this.players,
    this.teamId,
    this.teamName,
  });
  TeamPlayersModel.fromJson(Map<String, dynamic> json) {
    if (json['players'] != null) {
      final v = json['players'];
      final arr0 = <TeamPlayersModelPlayers>[];
      v.forEach((v) {
        arr0.add(TeamPlayersModelPlayers.fromJson(v));
      });
      players = arr0;
    }
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (players != null) {
      final v = players;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['players'] = arr0;
    }
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    return data;
  }
}
