// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'matches_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MatchesDetailsModel _$MatchesDetailsModelFromJson(Map<String, dynamic> json) =>
    MatchesDetailsModel(
      json['matchTime'] as String,
      json['matchDate'] as String,
      json['matchWeek'] as String,
      json['matchId'] as String,
      json['arenaId'] as String,
      json['arenaName'] as String,
      json['arenaAliasName'] as String,
      json['ArenaLogo'] as String,
      json['arenaContact'] as String,
      json['matchVideoPath'] as String,
      json['status'] as int,
      json['matchStartTime'] as String,
      json['matchEndTime'] as String,
      (json['courts'] as List<dynamic>?)
          ?.map((e) => Courts.fromJson(e as Map<String, dynamic>))
          .toList(),
      (json['teams'] as List<dynamic>)
          .map((e) => Teams.fromJson(e as Map<String, dynamic>))
          .toList(),
      (json['history'] as List<dynamic>?)
          ?.map((e) => GameHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['lockStatus'] as int,
      json['expired'] as bool,
      json['expiredTime'] as String,
      json['joined'] as bool,
      json['matchSubscribe'] as bool,
      json['halfMatchPrice'] as String,
      json['fullMatchPrice'] as String,
      json['summaryStatistics'] as bool,
      json['locked'] as int,
    );

Map<String, dynamic> _$MatchesDetailsModelToJson(
        MatchesDetailsModel instance) =>
    <String, dynamic>{
      'matchWeek': instance.matchWeek,
      'matchTime': instance.matchTime,
      'matchDate': instance.matchDate,
      'matchId': instance.matchId,
      'arenaId': instance.arenaId,
      'arenaName': instance.arenaName,
      'arenaAliasName': instance.arenaAliasName,
      'ArenaLogo': instance.arenaLogo,
      'arenaContact': instance.arenaContact,
      'matchVideoPath': instance.matchVideoPath,
      'status': instance.status,
      'locked': instance.locked,
      'matchStartTime': instance.matchStartTime,
      'matchEndTime': instance.matchEndTime,
      'courts': instance.courts,
      'teams': instance.teams,
      'history': instance.history,
      'lockStatus': instance.lockStatus,
      'expired': instance.expired,
      'expiredTime': instance.expiredTime,
      'joined': instance.joined,
      'matchSubscribe': instance.matchSubscribe,
      'halfMatchPrice': instance.halfMatchPrice,
      'fullMatchPrice': instance.fullMatchPrice,
      'summaryStatistics': instance.summaryStatistics,
    };

Courts _$CourtsFromJson(Map<String, dynamic> json) => Courts(
      json['courtId'] as String,
      json['courtName'] as String,
    );

Map<String, dynamic> _$CourtsToJson(Courts instance) => <String, dynamic>{
      'courtId': instance.courtId,
      'courtName': instance.courtName,
    };

Teams _$TeamsFromJson(Map<String, dynamic> json) => Teams(
      json['teamId'] as String,
      json['teamName'] as String,
      json['logo'] as String,
      json['score'] as int,
      json['status'] as int,
      json['locked'] as int,
      json['fragmentCount'] as int,
      json['teamBest'] == null
          ? null
          : TeamBest.fromJson(json['teamBest'] as Map<String, dynamic>),
      TeamScoreDetail.fromJson(json['teamScoreDetail'] as Map<String, dynamic>),
      (json['scoreTrend'] as List<dynamic>).map((e) => e as int).toList(),
      json['maxOffsetScore'] as int,
      json['CurrentScore'] as int,
    );

Map<String, dynamic> _$TeamsToJson(Teams instance) => <String, dynamic>{
      'teamId': instance.teamId,
      'teamName': instance.teamName,
      'logo': instance.logo,
      'score': instance.score,
      'status': instance.status,
      'locked': instance.locked,
      'fragmentCount': instance.fragmentCount,
      'teamBest': instance.teamBest,
      'teamScoreDetail': instance.teamScoreDetail,
      'scoreTrend': instance.scoreTrend,
      'maxOffsetScore': instance.maxOffsetScore,
      'CurrentScore': instance.currentScore,
    };

TeamBest _$TeamBestFromJson(Map<String, dynamic> json) => TeamBest(
      ScoreKing.fromJson(json['scoreKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['threePointKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['freeThrowKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['assistKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['reboundKing'] as Map<String, dynamic>),
      ScoreKing.fromJson(json['mvp'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TeamBestToJson(TeamBest instance) => <String, dynamic>{
      'scoreKing': instance.scoreKing,
      'threePointKing': instance.threePointKing,
      'freeThrowKing': instance.freeThrowKing,
      'assistKing': instance.assistKing,
      'reboundKing': instance.reboundKing,
      'mvp': instance.mvp,
    };

ScoreKing _$ScoreKingFromJson(Map<String, dynamic> json) => ScoreKing(
      json['playerId'] as String,
      json['photo'] as String,
      json['number'] as String,
      json['score'] as String,
      json['rate'] as String,
      json['hit'] as int,
      json['rebound'] as int,
      json['assist'] as int,
      json['locked'] as int,
    );

Map<String, dynamic> _$ScoreKingToJson(ScoreKing instance) => <String, dynamic>{
      'playerId': instance.playerId,
      'photo': instance.photo,
      'number': instance.number,
      'score': instance.score,
      'rate': instance.rate,
      'hit': instance.hit,
      'rebound': instance.rebound,
      'assist': instance.assist,
      'locked': instance.locked,
    };

TeamScoreDetail _$TeamScoreDetailFromJson(Map<String, dynamic> json) =>
    TeamScoreDetail(
      json['totalScore'] as int,
      json['markedScore'] as int,
      json['reboundCount'] as int,
      json['offensiveReboundCount'] as int,
      json['defensiveReboundCount'] as int,
      json['assistCount'] as int,
      json['shootCount'] as int,
      json['shootHit'] as int,
      json['shootRate'] as String,
      json['threePointShootCount'] as int,
      json['threePointShootHit'] as int,
      json['threePointShootRate'] as String,
      json['twoPointShootCount'] as int,
      json['twoPointShootHit'] as int,
      json['twoPointShootRate'] as String,
      json['freeThrowShootCount'] as int,
      json['freeThrowShootHit'] as int,
      json['freeThrowShootRate'] as String,
    );

Map<String, dynamic> _$TeamScoreDetailToJson(TeamScoreDetail instance) =>
    <String, dynamic>{
      'totalScore': instance.totalScore,
      'markedScore': instance.markedScore,
      'reboundCount': instance.reboundCount,
      'offensiveReboundCount': instance.offensiveReboundCount,
      'defensiveReboundCount': instance.defensiveReboundCount,
      'assistCount': instance.assistCount,
      'shootCount': instance.shootCount,
      'shootHit': instance.shootHit,
      'shootRate': instance.shootRate,
      'threePointShootCount': instance.threePointShootCount,
      'threePointShootHit': instance.threePointShootHit,
      'threePointShootRate': instance.threePointShootRate,
      'twoPointShootCount': instance.twoPointShootCount,
      'twoPointShootHit': instance.twoPointShootHit,
      'twoPointShootRate': instance.twoPointShootRate,
      'freeThrowShootCount': instance.freeThrowShootCount,
      'freeThrowShootHit': instance.freeThrowShootHit,
      'freeThrowShootRate': instance.freeThrowShootRate,
    };

GameHistory _$GameHistoryFromJson(Map<String, dynamic> json) => GameHistory(
      json['leftScore'] as int,
      json['rightScore'] as int,
      json['winner'] as int,
      json['matchTime'] as String,
    );

Map<String, dynamic> _$GameHistoryToJson(GameHistory instance) =>
    <String, dynamic>{
      'leftScore': instance.leftScore,
      'rightScore': instance.rightScore,
      'winner': instance.winner,
      'matchTime': instance.matchTime,
    };
