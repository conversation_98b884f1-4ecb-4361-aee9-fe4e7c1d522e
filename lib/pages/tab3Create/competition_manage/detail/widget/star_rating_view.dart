import 'package:flutter/material.dart';

class StarRating extends StatefulWidget {
  final int rating; // 当前评分
  final int starCount; // 星星数量
  final double starSize; // 星星大小
  final double spacing; // 星星之间的间距
  final ValueChanged<int> onRatingChanged; // 评分变化回调
  final Color? filledColor; // 填充颜色（可选，用于着色）
  final Color? emptyColor; // 未填充颜色（可选，用于着色）

  const StarRating({
    super.key,
    required this.rating,
    this.starCount = 5,
    this.starSize = 24,
    this.spacing = 8,
    required this.onRatingChanged,
    this.filledColor,
    this.emptyColor,
  });

  @override
  State<StarRating> createState() => _StarRatingState();
}

class _StarRatingState extends State<StarRating> {
  // int _currentRating = 0; // 初始评分为0，全部未选中

  // @override
  // void initState() {
  //   super.initState();
  //   _currentRating = widget.rating;
  // }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.starCount, (index) {
        // 最后一个星星不需要右边距
        final margin = index < widget.starCount - 1
            ? EdgeInsets.only(right: widget.spacing)
            : EdgeInsets.zero;

        return Container(
          margin: margin,
          child: GestureDetector(
            onTap: () {
              widget.onRatingChanged(index + 1);
            },
            child: _buildStar(index),
          ),
        );
      }),
    );
  }

  Widget _buildStar(int index) {
    // 判断是否已选中
    bool isFilled = index < widget.rating;

    return Image.asset(
      isFilled ? 'assets/images/star_full.png' : 'assets/images/star_empty.png',
      width: widget.starSize,
      height: widget.starSize,
      color: isFilled ? widget.filledColor : widget.emptyColor,
      fit: BoxFit.contain,
    );
  }
}
