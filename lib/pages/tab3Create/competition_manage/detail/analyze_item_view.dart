import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';

class AnalyzeItemView extends StatelessWidget {
  final double left;
  final double right;
  final String title;
  final bool needCalculate;
  const AnalyzeItemView(
      {super.key,
      required this.left,
      required this.right,
      required this.title,
      this.needCalculate = true});

  @override
  Widget build(BuildContext context) {
    final leftText =
        needCalculate ? left.toInt().toString() : Utils.formatToPercentage(left);
    final rightText =
        needCalculate ? right.toInt().toString() : Utils.formatToPercentage(right);
    var l = left;
    var r = right;
    if (needCalculate && (left + right) > 0) {
      l = left / (left + right);
      r = 1 - l;
    }
    var leftColor = Colours.color7732ED;
    var rightColor = Colours.colorE282FF;
    if (r != l) {
      leftColor =
          l > r ? Colours.color7732ED : Colours.color7732ED.withOpacity(0.2);
      rightColor =
          l > r ? Colours.colorE282FF.withOpacity(0.1) : Colours.colorE282FF;
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              leftText,
              style: TextStyles.medium.copyWith(fontSize: 14.sp),
            ),
            Text(
              title,
              style: TextStyles.regular
                  .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
            ),
            Text(
              rightText,
              style: TextStyles.medium.copyWith(fontSize: 14.sp),
            ),
          ],
        ),
        SizedBox(
          height: 12.w,
        ),
        LayoutBuilder(
          builder: (context, constraints) => Container(
            height: 6.w,
            decoration: BoxDecoration(
              color: Colours.color22222D,
              borderRadius: BorderRadius.circular(3.w),
            ),
            child: Stack(
              children: [
                Positioned(
                    top: 0,
                    bottom: 0,
                    right: constraints.maxWidth / 2,
                    left: (1 - l) * constraints.maxWidth / 2,
                    child: Container(
                      decoration: BoxDecoration(
                        color: leftColor,
                        borderRadius:
                            BorderRadius.horizontal(left: Radius.circular(3.w)),
                      ),
                    )),
                Positioned(
                    top: 0,
                    bottom: 0,
                    left: constraints.maxWidth / 2,
                    right:
                        constraints.maxWidth / 2 - r * constraints.maxWidth / 2,
                    child: Container(
                      decoration: BoxDecoration(
                        color: rightColor,
                        borderRadius: BorderRadius.horizontal(
                            right: Radius.circular(3.w)),
                      ),
                    )),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
