import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/mactches_info_model.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'models/section_score_model.dart';

class MatchDetailsState {
  final ScrollController scrollController = ScrollController();
  var topViewHeight = 100.0.w;
  var offset = 140.0;
  var showTop = false.obs;
  var init = false.obs;
  var allLocked = false.obs;
  List<SectionScoreModel>? sectionScoreList;
  var model = MatchesInfoModel().obs;
  var leftTeam = MatchesInfoModelTeamsTeamBest().obs;
  var rightTeam = MatchesInfoModelTeamsTeamBest().obs;
  var matchVideoPath = ''.obs;
  late VideoController videoController;
}
